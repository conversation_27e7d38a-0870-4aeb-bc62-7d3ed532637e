
import { createStore } from 'vuex'
import { store as user, UserStore, UserState } from './modules/user/index'
import { store as app, AppStore, AppState } from './modules/app/index'


export interface RootState {
  user: UserState,
  app: AppStore
}

export type Store = UserStore<Pick<RootState, 'user'>> & AppStore<Pick<RootState, 'app'>>

export const store = createStore({
  modules: {
    user,
    app
  }
})

export function useStore(): Store {
  return store as Store
}