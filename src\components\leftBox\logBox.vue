<template>
  <div class="box" @mouseleave="dataMove = true" @mousemove="dataMove = false">
    <div class="title">
      <span class="describe">日志</span>
      <span class="close" @click.stop="close">关闭</span>
    </div>
    <div class="body" ref="logBox">
      <el-timeline
        direction="vertical"
        :active="1"
        v-if="list && list.length > 0"
      >
        <el-timeline-item
          v-for="item in list"
          :key="item.sendTime"
          :timestamp="dayjs(item.sendTime).format('YYYY-MM-DD HH:mm:ss')"
        >
          {{ item.messages }}
        </el-timeline-item>
      </el-timeline>
      <p v-else class="not-text">暂无日志信息</p>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  ref,
  reactive,
  defineExpose,
  onMounted,
  nextTick,
  defineEmits,
} from 'vue'
import dayjs from 'dayjs'
const emit = defineEmits(['show'])
let list = reactive<Array<{ messages: string; sendTime: string }>>([])
defineExpose({ list })
let logBox = ref({
  scrollTop: 0,
  scrollHeight: 0,
})
let dataMove = ref(false) // 鼠标是否移入
onMounted(() => {
  init()
})
// 页面初始化
const init = () => {
  getData()
}
// 获取webSokect数据
const getData = () => {
  const ws = new WebSocket(
    `${(window as any).GVJ.URLS.pluginServer}/getKeyMessages`
  )
  ws.onopen = () => {
    console.log('系统日志链接成功!')
  }
  ws.onmessage = e => {
    list.splice(0, list.length, ...JSON.parse(e.data))
    // 更新数据时, 如果鼠标不在日志区域, 则追踪至列表最下方
    if (!dataMove.value) {
      nextTick(() => {
        logBox.value.scrollTop = logBox.value.scrollHeight
      })
    }
  }
}

// 关闭弹层
const close = () => {
  emit('show', 'systemLog')
}
</script>
<style scoped>
.box {
  width: 16.146vw;
  height: 58.8889vh;
  min-width: 28.7037vh;
  min-height: 58.8889vh;
  background-color: rgba(16, 37, 63, 0.7);
  position: absolute;
  left: 0.525vw;
  top: 34vh;
  z-index: 2002;
  border-radius: 5px;
  padding: 0 10px;
  box-sizing: border-box;
  color: #ffffff;
}

.box .title {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  padding: 10px 10px 15px;
}

.box .title .close {
  cursor: pointer;
}

.box .title > span:nth-child(1) {
  padding-left: 10px;
  border-left: 4px solid #0ff;
}

.box .body {
  height: 90%;
  padding: 0 7px;
  overflow-y: auto;
}

.box .body .not-text {
  font-size: 14px;
  text-align: center;
  margin-top: 100px;
  color: #909399;
}

::-webkit-scrollbar {
  width: 6px;
  height: 8px;
  position: absolute;
  right: 0;
  bottom: 0;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: #00ecff !important;
  border-radius: 0.625rem;
}

.box .body >>> .el-timeline-item__content {
  color: aqua;
}
</style>
