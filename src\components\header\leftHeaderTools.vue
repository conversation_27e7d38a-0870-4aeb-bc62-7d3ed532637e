<template>
  <div class="flex pl-2 bg">
    <div
      class="
        border-2
        w-10
        h-10
        flex
        justify-center
        items-center
        mr-2
        mt-2
        cursor-pointer
        item
      "
      v-for="item in arr"
      :key="item"
      @click="go(item.url)"
    >
      <el-tooltip class="item" effect="dark" :content="item.tittle">
        <div><img :src="geticon(item)" style="width: 20px" /></div>
      </el-tooltip>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
// const arr = [
//   { name: '图层',  url: 'http://***************:19101'},
//   { name: '插件管理', url: 'http://***************:19101' },
//   { name: '图标库', url: 'http://***************:4000' },
//   { name: '授权管理', url: 'http://***************:19102' },
// ]
const arr = window.GVJ.URLS.linkList
const go = (url: string): void => {
  if (!url) {
    return
  }
  window.open(url)
}
// 获取图标
const geticon = data => {
  return `/image/${data.icon}`
}
</script>

<style lang="sass" scoped>
.bg
  height: 92px
  width: 998px
  background: url('../../assets/images/backside.png') repeat-x
  background-position-y: -10px
  .item
    border-color: #144759c7
  .item:hover
    border-color: #74bedb !important
</style>
