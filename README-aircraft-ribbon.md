# 飞机模型飘带效果实现

## 🎯 项目概述

本项目为基于 Cesium 的 3D 地球应用实现了一个完整的飞机模型飘带效果系统。该系统能够根据飞机的实时位置和姿态变化动态生成美观的飘带轨迹，适用于航空仿真、飞行演示、军事可视化等场景。

## ✨ 核心特性

### 🛩️ 智能飘带生成

- **实时更新**: 根据飞机位置和姿态实时更新飘带轨迹
- **姿态感知**: 基于飞机姿态变化（俯仰、偏航、滚转）智能添加飘带点
- **距离优化**: 根据移动距离阈值避免过度密集的点生成
- **尾部定位**: 精确计算飞机尾部位置作为飘带起始点
- **智能宽度计算**: 飘带宽度根据 GLB 模型在屏幕上的实际渲染尺寸动态调整
- **翼展数据库**: 内置多种飞机型号翼展数据，支持精确宽度计算
- **翻滚响应**: 飘带宽度根据飞机翻滚角度动态变化，模拟真实空气动力学效果
- **缩放自适应**: 飘带宽度随模型缩放、相机距离、视角变化自动调整

### 🎨 视觉效果

- **发光材质**: 使用 PolylineGlowMaterialProperty 创建发光效果
- **渐变透明**: 支持飘带透明度渐变
- **颜色定制**: 支持自定义飘带颜色，可根据阵营区分
- **宽度调节**: 可调节飘带宽度以适应不同场景

### ⚡ 性能优化

- **点数限制**: 自动限制飘带最大点数，防止内存溢出
- **智能清理**: 自动移除最旧的飘带点
- **更新策略**: 基于距离和姿态变化的智能更新策略
- **批量处理**: 支持多架飞机的批量飘带管理

## 📁 文件结构

```
src/
├── utils/
│   ├── effectManager.ts          # 核心特效管理器
│   ├── modelControler.ts         # 模型管理器（已集成飘带功能）
│   ├── aircraftRibbonDemo.ts     # 飘带效果演示类
│   └── primitive.js              # 基础图元管理
├── examples/
│   └── aircraftRibbonExample.ts  # 使用示例和API封装
docs/
└── aircraft-ribbon-effect.md     # 详细使用文档
public/
└── aircraft-ribbon-demo.html     # 在线演示页面
```

## 🚀 快速开始

### 1. 基础集成

```typescript
import { EffectManager } from './utils/effectManager'
import { ModelManager } from './utils/modelControler'

// 初始化特效管理器
const effectManager = new EffectManager(viewer, wsUrl)

// 初始化模型管理器（自动集成飘带功能）
const modelManager = new ModelManager(viewer, effectManager)

// 创建飞机模型（自动添加飘带效果）
await modelManager.createNewModel({
  na: 'aircraft_001',
  ic: 'f18_aircraft', // 包含'aircraft'关键词会自动添加飘带
  lo: 116.0,
  la: 39.0,
  al: 5000,
  si: 'blue',
  ya: 0,
  pi: 0,
  ro: 0,
  type: 'aircraft',
})
```

### 2. 手动添加飘带

```typescript
import { AircraftRibbonExample } from './examples/aircraftRibbonExample'

const example = new AircraftRibbonExample(viewer)

// 为现有飞机添加飘带
example.addRibbonToExistingAircraft('aircraft_id', {
  color: Cesium.Color.CYAN.withAlpha(0.8),
  width: 10.0,
  maxPoints: 100,
})
```

### 3. 演示功能

```typescript
// 单机演示
example.createSingleAircraftDemo()

// 编队演示
example.createFighterFormation()

// 螺旋飞行演示
example.createSpiralFlightPath({ lon: 116.0, lat: 39.0, alt: 5000 })

// 翻滚演示 - 观察飘带宽度随翻滚角度变化
example.createRollDemo()

// 缩放测试 - 观察飘带宽度随模型缩放变化
example.createScaleTest()
```

## 🔧 配置参数

### 飘带配置

```typescript
const ribbonConfig = {
  maxPoints: 50, // 最大点数
  wingSpan: 30.0, // 飞机翼展宽度（米）
  ribbonWidth: 2.0, // 飘带基础宽度（像素）
  color: Cesium.Color.CYAN.withAlpha(0.8), // 颜色
  minDistance: 10.0, // 最小距离阈值
  fadeAlpha: 0.1, // 渐变透明度
  rollSensitivity: 1.0, // 翻滚角度敏感度
}
```

### 效果配置

```typescript
const effectConfig = {
  type: 7, // 飘带效果类型
  modelId: 'aircraft_id',
  state: 0, // 0=创建, 1=销毁
  color: Cesium.Color.CYAN,
  customParams: ribbonConfig,
}
```

## 🎮 在线演示

打开 `public/aircraft-ribbon-demo.html` 可以体验完整的飞机飘带效果演示，包括：

- **单机演示**: 单架飞机的飘带效果
- **编队演示**: 三机编队的不同颜色飘带
- **螺旋飞行**: 螺旋上升的飞行轨迹
- **翻滚演示**: 飞机翻滚时飘带宽度动态变化效果
- **实时配置**: 动态调整飘带宽度、长度、颜色
- **相机控制**: 跟踪飞机、自由视角、俯视图

## 🔄 工作原理

### 1. 飘带点生成策略

```typescript
// 基于距离判断
if (distance > minDistance) addPoint = true

// 基于姿态变化判断
const orientationChange = Math.acos(Math.abs(dot)) * 2
if (orientationChange > 5°) addPoint = true
```

### 2. 尾部位置计算

```typescript
// 飞机尾部偏移（本地坐标系）
const tailOffset = new Cesium.Cartesian3(-20, 0, -2)

// 转换到世界坐标系
const worldOffset = rotationMatrix * tailOffset
const ribbonStartPoint = aircraftPosition + worldOffset
```

### 3. 实时更新机制

```typescript
// 使用CallbackProperty实现实时更新
positions: new Cesium.CallbackProperty(() => {
  return this.updateRibbonPositions(effect)
}, false)
```

## 🎯 应用场景

- **航空仿真**: 飞行训练模拟器中的飞机轨迹显示
- **军事演示**: 战术演示中的飞机编队飞行
- **航展可视化**: 航展飞行表演的实时轨迹展示
- **飞行回放**: 历史飞行数据的可视化回放
- **游戏开发**: 飞行游戏中的特效系统

## 🔧 自定义扩展

### 1. 自定义飘带材质

```typescript
// 创建自定义材质
const customMaterial = new Cesium.PolylineGlowMaterialProperty({
  glowPower: 0.5,
  color: Cesium.Color.fromCssColorString('#ff6b6b').withAlpha(0.8),
})
```

### 2. 多段飘带效果

```typescript
// 为不同飞行阶段设置不同颜色
const phaseColors = {
  takeoff: Cesium.Color.GREEN,
  cruise: Cesium.Color.BLUE,
  landing: Cesium.Color.RED,
}
```

### 3. 粒子系统集成

```typescript
// 结合粒子系统创建更丰富的效果
const particleSystem = new Cesium.ParticleSystem({
  image: '/effects/smoke.png',
  modelMatrix: aircraftMatrix,
  // ... 其他配置
})
```

## 📈 性能建议

1. **合理设置点数**: 根据场景需求调整 maxPoints（推荐 20-100）
2. **优化更新频率**: 增加 minDistance 减少更新频率
3. **限制同时显示**: 避免同时显示过多飘带
4. **及时清理**: 销毁不需要的飘带效果

## 🐛 故障排除

### 常见问题

1. **飘带不显示**: 检查模型 ID 和 position 属性
2. **更新不及时**: 调整 minDistance 和姿态阈值
3. **性能问题**: 减少 maxPoints 和同时显示数量

### 调试方法

```typescript
// 启用调试日志
console.log('飘带数据:', effect.ribbonData)
console.log('当前点数:', effect.ribbonData?.positions.length)
```

## 🧮 宽度计算算法

### 智能宽度计算原理

飘带宽度的计算基于以下几个关键因素：

1. **模型屏幕尺寸计算**

   ```typescript
   // 计算模型在屏幕上的像素宽度
   const distance = Cesium.Cartesian3.distance(cameraPosition, aircraftPosition)
   const fov = viewer.camera.frustum.fov
   const pixelsPerMeter =
     canvas.clientHeight / 2 / (distance * Math.tan(fov / 2))
   const screenWidth = wingSpanMeters * pixelsPerMeter * modelScale
   ```

2. **翼展数据库**

   ```typescript
   const aircraftTypes = {
     f35: 10.7, // F-35 Lightning II
     f22: 13.6, // F-22 Raptor
     f18: 12.3, // F-18 Super Hornet
     // ... 更多飞机型号
   }
   ```

3. **翻滚角度影响**
   ```typescript
   const rollFactor = Math.abs(Math.cos(rollAngle))
   const finalWidth = modelScreenWidth * rollFactor * 0.8
   ```

### 自适应特性

- **距离自适应**: 相机距离越远，飘带相对宽度越小
- **缩放响应**: 模型缩放变化时，飘带宽度同步调整
- **视角补偿**: 不同相机视角下保持视觉一致性
- **最小/最大限制**: 防止飘带过窄或过宽影响视觉效果

## 📝 更新日志

- **v1.0.0**: 初始版本，基础飘带功能
- **v1.1.0**: 添加姿态感知和性能优化
- **v1.2.0**: 集成到 ModelManager，支持自动添加
- **v1.3.0**: 添加演示页面和完整文档
- **v1.4.0**: 智能宽度计算，基于 GLB 模型实际渲染尺寸动态调整

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！
