# 飞机模型飘带效果实现

## 🎯 项目概述

本项目为基于Cesium的3D地球应用实现了一个完整的飞机模型飘带效果系统。该系统能够根据飞机的实时位置和姿态变化动态生成美观的飘带轨迹，适用于航空仿真、飞行演示、军事可视化等场景。

## ✨ 核心特性

### 🛩️ 智能飘带生成
- **实时更新**: 根据飞机位置和姿态实时更新飘带轨迹
- **姿态感知**: 基于飞机姿态变化（俯仰、偏航、滚转）智能添加飘带点
- **距离优化**: 根据移动距离阈值避免过度密集的点生成
- **尾部定位**: 精确计算飞机尾部位置作为飘带起始点

### 🎨 视觉效果
- **发光材质**: 使用PolylineGlowMaterialProperty创建发光效果
- **渐变透明**: 支持飘带透明度渐变
- **颜色定制**: 支持自定义飘带颜色，可根据阵营区分
- **宽度调节**: 可调节飘带宽度以适应不同场景

### ⚡ 性能优化
- **点数限制**: 自动限制飘带最大点数，防止内存溢出
- **智能清理**: 自动移除最旧的飘带点
- **更新策略**: 基于距离和姿态变化的智能更新策略
- **批量处理**: 支持多架飞机的批量飘带管理

## 📁 文件结构

```
src/
├── utils/
│   ├── effectManager.ts          # 核心特效管理器
│   ├── modelControler.ts         # 模型管理器（已集成飘带功能）
│   ├── aircraftRibbonDemo.ts     # 飘带效果演示类
│   └── primitive.js              # 基础图元管理
├── examples/
│   └── aircraftRibbonExample.ts  # 使用示例和API封装
docs/
└── aircraft-ribbon-effect.md     # 详细使用文档
public/
└── aircraft-ribbon-demo.html     # 在线演示页面
```

## 🚀 快速开始

### 1. 基础集成

```typescript
import { EffectManager } from './utils/effectManager'
import { ModelManager } from './utils/modelControler'

// 初始化特效管理器
const effectManager = new EffectManager(viewer, wsUrl)

// 初始化模型管理器（自动集成飘带功能）
const modelManager = new ModelManager(viewer, effectManager)

// 创建飞机模型（自动添加飘带效果）
await modelManager.createNewModel({
  na: 'aircraft_001',
  ic: 'f18_aircraft',  // 包含'aircraft'关键词会自动添加飘带
  lo: 116.0,
  la: 39.0,
  al: 5000,
  si: 'blue',
  ya: 0,
  pi: 0,
  ro: 0,
  type: 'aircraft'
})
```

### 2. 手动添加飘带

```typescript
import { AircraftRibbonExample } from './examples/aircraftRibbonExample'

const example = new AircraftRibbonExample(viewer)

// 为现有飞机添加飘带
example.addRibbonToExistingAircraft('aircraft_id', {
  color: Cesium.Color.CYAN.withAlpha(0.8),
  width: 10.0,
  maxPoints: 100
})
```

### 3. 演示功能

```typescript
// 单机演示
example.createSingleAircraftDemo()

// 编队演示
example.createFighterFormation()

// 螺旋飞行演示
example.createSpiralFlightPath({ lon: 116.0, lat: 39.0, alt: 5000 })
```

## 🔧 配置参数

### 飘带配置
```typescript
const ribbonConfig = {
  maxPoints: 50,        // 最大点数
  width: 8.0,          // 线宽
  color: Cesium.Color.CYAN.withAlpha(0.8),  // 颜色
  minDistance: 10.0,   // 最小距离阈值
  fadeAlpha: 0.1       // 渐变透明度
}
```

### 效果配置
```typescript
const effectConfig = {
  type: 7,             // 飘带效果类型
  modelId: 'aircraft_id',
  state: 0,            // 0=创建, 1=销毁
  color: Cesium.Color.CYAN,
  customParams: ribbonConfig
}
```

## 🎮 在线演示

打开 `public/aircraft-ribbon-demo.html` 可以体验完整的飞机飘带效果演示，包括：

- **单机演示**: 单架飞机的飘带效果
- **编队演示**: 三机编队的不同颜色飘带
- **螺旋飞行**: 螺旋上升的飞行轨迹
- **实时配置**: 动态调整飘带宽度、长度、颜色
- **相机控制**: 跟踪飞机、自由视角、俯视图

## 🔄 工作原理

### 1. 飘带点生成策略
```typescript
// 基于距离判断
if (distance > minDistance) addPoint = true

// 基于姿态变化判断  
const orientationChange = Math.acos(Math.abs(dot)) * 2
if (orientationChange > 5°) addPoint = true
```

### 2. 尾部位置计算
```typescript
// 飞机尾部偏移（本地坐标系）
const tailOffset = new Cesium.Cartesian3(-20, 0, -2)

// 转换到世界坐标系
const worldOffset = rotationMatrix * tailOffset
const ribbonStartPoint = aircraftPosition + worldOffset
```

### 3. 实时更新机制
```typescript
// 使用CallbackProperty实现实时更新
positions: new Cesium.CallbackProperty(() => {
  return this.updateRibbonPositions(effect)
}, false)
```

## 🎯 应用场景

- **航空仿真**: 飞行训练模拟器中的飞机轨迹显示
- **军事演示**: 战术演示中的飞机编队飞行
- **航展可视化**: 航展飞行表演的实时轨迹展示
- **飞行回放**: 历史飞行数据的可视化回放
- **游戏开发**: 飞行游戏中的特效系统

## 🔧 自定义扩展

### 1. 自定义飘带材质
```typescript
// 创建自定义材质
const customMaterial = new Cesium.PolylineGlowMaterialProperty({
  glowPower: 0.5,
  color: Cesium.Color.fromCssColorString('#ff6b6b').withAlpha(0.8)
})
```

### 2. 多段飘带效果
```typescript
// 为不同飞行阶段设置不同颜色
const phaseColors = {
  takeoff: Cesium.Color.GREEN,
  cruise: Cesium.Color.BLUE,
  landing: Cesium.Color.RED
}
```

### 3. 粒子系统集成
```typescript
// 结合粒子系统创建更丰富的效果
const particleSystem = new Cesium.ParticleSystem({
  image: '/effects/smoke.png',
  modelMatrix: aircraftMatrix,
  // ... 其他配置
})
```

## 📈 性能建议

1. **合理设置点数**: 根据场景需求调整maxPoints（推荐20-100）
2. **优化更新频率**: 增加minDistance减少更新频率
3. **限制同时显示**: 避免同时显示过多飘带
4. **及时清理**: 销毁不需要的飘带效果

## 🐛 故障排除

### 常见问题
1. **飘带不显示**: 检查模型ID和position属性
2. **更新不及时**: 调整minDistance和姿态阈值
3. **性能问题**: 减少maxPoints和同时显示数量

### 调试方法
```typescript
// 启用调试日志
console.log('飘带数据:', effect.ribbonData)
console.log('当前点数:', effect.ribbonData?.positions.length)
```

## 📝 更新日志

- **v1.0.0**: 初始版本，基础飘带功能
- **v1.1.0**: 添加姿态感知和性能优化
- **v1.2.0**: 集成到ModelManager，支持自动添加
- **v1.3.0**: 添加演示页面和完整文档

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！
