/*
 * @Author: 老范
 * @Date: 2025-03-20 17:50:45
 * @LastEditors: 老范
 * @LastEditTime: 2025-04-29 10:40:20
 * @Description: 请填写简介
 */
window.GVJ.URLS = {
  version: '*******',
  title: '领航数字地球',
  pluginServer: 'http://*************:8080/', // 后端服务
  socketServer: 'ws://*************:38838/', // websocket服务
  sensorColor: {
    color: `rgba(0,0,0,0.1)`, // 球面颜色
    lineColor: `rgba(0,255,42,0.5)`, // 线颜色
    scanPlaneColor: `rgba(0,0,255,0.1)`, //扫描面颜色
  },
  icenterServer: '',
  jbServer: '',//军标服务地址
  spaceServer: '',//空间分析地址
  linkList: [
    {
      tittle: 'Icenter',
      icon: 'icenter.png',
      url: 'http://**************:8310'
    },
    {
      tittle: '三维示例沙盒',
      icon: '3D.png',
      url: 'http://**************:3000'
    },
    {
      tittle: '二维示例沙盒',
      icon: '2D.png',
      url: 'http://**************:2000'
    }
  ]
}