/*
 * @Author: 老范
 * @Date: 2025-04-10 14:10:40
 * @LastEditors: 老范
 * @LastEditTime: 2025-04-27 15:16:34
 * @Description: 请填写简介
 */
// import * as Cesium from 'cesium'

export class TrailManager {
  constructor(viewer) {
    this.viewer = viewer
    this.trails = new Map()
    this.maxPoints = 200
    this.settings = {
      color: Cesium.Color.RED,
      width: 12.0,
    }
    this.polylineCollection = new Cesium.PolylineCollection()
    viewer.scene.primitives.add(this.polylineCollection)
    this._initWorker()
  }

  addTrail(trailId, initialData, options = {}) {
    this.worker.postMessage({
      type: 'CREATE_TRAIL',
      trailId,
      rawData: initialData,
      settings: this.settings,
    })
  }

  updateTrail(trailId, newData) {
    this.worker.postMessage({
      type: 'UPDATE_TRAIL',
      trailId,
      rawData: newData,
    })
  }
  _initWorker() {
    const workerUrl = new URL('./trailDataProcessor.js', import.meta.url)
    this.worker = new Worker(workerUrl, { type: 'module' })

    this.worker.onmessage = e => {
      const { type, trailId, positions, settings, error } = e.data

      if (error) {
        console.error(`Trail ${trailId} error:`, error)
        return
      }

      switch (type) {
        case 'TRAIL_UPDATE':
          this._updateGeometry(trailId, positions, this.settings)
          break
        case 'TRAIL_CREATED':
          this._createGeometry(trailId, positions, this.settings)
          break
      }

      //   document.getElementById('trailCount').textContent = this.trails.size;
    }
  }
  convertFloat64ArrayToCartesian3(float64Array) {
    // 验证数据长度是否为3的倍数
    if (float64Array.length % 3 !== 0) {
      throw new Error('Invalid Float64Array length. Must be a multiple of 3.')
    }

    const positions = []
    const length = float64Array.length

    // 每次读取三个元素生成一个Cartesian3
    for (let i = 0; i < length; i += 3) {
      const x = float64Array[i]
      const y = float64Array[i + 1]
      const z = float64Array[i + 2]
      positions.push(new Cesium.Cartesian3(x, y, z))
    }

    return positions
  }
  _createGeometry(trailId, positions, settings) {
    const cartesianPositions = this.convertFloat64ArrayToCartesian3(positions)
    const polyLine = this.polylineCollection.add({
      positions: cartesianPositions,
      // positions: Cesium.Cartesian3.fromDegreesArrayHeights([
      //   110, 35, 1000, 130, 45, 1000, 135, 46, 1000,
      // ]),
      with: 2.0,
      material: Cesium.Material.fromType('Color', {
        color: Cesium.Color.YELLOW,
      }),
    })
    this.trails.set(trailId, {
      polyLine,
    })
  }

  _updateGeometry(trailId, positions, settings) {
    const cartesianPositions = this.convertFloat64ArrayToCartesian3(positions)
    const trail = this.trails.get(trailId)
    if (!trail) return
    trail.polyLine.positions = cartesianPositions
  }
  removeTrail(trailId) {
    const trail = this.trails.get(trailId)
    if (trail) {
      this.polylineCollection.remove(trail.polyLine)
      this.trails.delete(trailId)
      this.worker.postMessage({
        type: 'REMOVE_TRAIL',
        trailId,
      })
    }
  }
}
