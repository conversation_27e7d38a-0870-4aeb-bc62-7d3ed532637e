/**
 * 飞机飘带效果使用示例
 * 展示如何在实际项目中使用飞机飘带效果
 */

import { EffectManager, EffectConfig } from '../utils/effectManager'
import { AircraftRibbonDemo } from '../utils/aircraftRibbonDemo'

export class AircraftRibbonExample {
  private viewer: Cesium.Viewer
  private effectManager: EffectManager
  private demo: AircraftRibbonDemo

  constructor(viewer: Cesium.Viewer) {
    this.viewer = viewer
    
    // 初始化特效管理器
    this.effectManager = new EffectManager(
      viewer,
      'ws://localhost:8080/effects' // 替换为实际的WebSocket地址
    )
    
    this.demo = new AircraftRibbonDemo(viewer, this.effectManager)
  }

  /**
   * 为现有飞机模型添加飘带效果
   */
  public addRibbonToExistingAircraft(aircraftId: string, options?: {
    color?: Cesium.Color
    width?: number
    maxPoints?: number
  }): void {
    const aircraft = this.viewer.entities.getById(aircraftId)
    if (!aircraft) {
      console.warn(`Aircraft with id ${aircraftId} not found`)
      return
    }

    const config: EffectConfig = {
      type: 7, // 飞机飘带效果
      modelId: aircraftId,
      state: 0, // 创建状态
      color: options?.color || Cesium.Color.CYAN.withAlpha(0.8),
      customParams: {
        maxPoints: options?.maxPoints || 50,
        width: options?.width || 8.0,
        minDistance: 10.0,
        fadeAlpha: 0.1
      }
    }

    this.effectManager.createEffect(config, aircraftId, 7)
  }

  /**
   * 移除飞机飘带效果
   */
  public removeRibbonFromAircraft(aircraftId: string): void {
    this.effectManager.destroyEffectByModelId(aircraftId)
  }

  /**
   * 批量为多架飞机添加飘带效果
   */
  public addRibbonToMultipleAircraft(aircraftIds: string[], options?: {
    colors?: Cesium.Color[]
    width?: number
    maxPoints?: number
  }): void {
    aircraftIds.forEach((id, index) => {
      const color = options?.colors?.[index] || this.generateRandomColor()
      this.addRibbonToExistingAircraft(id, {
        color,
        width: options?.width,
        maxPoints: options?.maxPoints
      })
    })
  }

  /**
   * 创建自定义飞行路径的飞机
   */
  public createCustomFlightPath(waypoints: Array<{
    lon: number
    lat: number
    alt: number
    time: number
  }>, aircraftConfig?: {
    id?: string
    name?: string
    modelUri?: string
    color?: Cesium.Color
    ribbonColor?: Cesium.Color
  }): void {
    const startTime = Cesium.JulianDate.now()
    const property = new Cesium.SampledPositionProperty()

    // 添加路径点
    waypoints.forEach(point => {
      const time = Cesium.JulianDate.addSeconds(startTime, point.time, new Cesium.JulianDate())
      const position = Cesium.Cartesian3.fromDegrees(point.lon, point.lat, point.alt)
      property.addSample(time, position)
    })

    property.setInterpolationOptions({
      interpolationDegree: 2,
      interpolationAlgorithm: Cesium.HermitePolynomialApproximation
    })

    const aircraftId = aircraftConfig?.id || `aircraft_${Date.now()}`

    // 创建飞机实体
    const aircraft = this.viewer.entities.add({
      id: aircraftId,
      name: aircraftConfig?.name || '自定义飞机',
      position: property,
      orientation: new Cesium.VelocityOrientationProperty(property),
      model: {
        uri: aircraftConfig?.modelUri || '/assets/models/f18.glb',
        minimumPixelSize: 64,
        maximumScale: 200,
        color: aircraftConfig?.color || Cesium.Color.WHITE
      }
    })

    // 添加飘带效果
    this.addRibbonToExistingAircraft(aircraftId, {
      color: aircraftConfig?.ribbonColor || Cesium.Color.CYAN.withAlpha(0.8)
    })

    // 设置时钟
    const duration = Math.max(...waypoints.map(w => w.time))
    const endTime = Cesium.JulianDate.addSeconds(startTime, duration, new Cesium.JulianDate())
    
    this.viewer.clock.startTime = startTime.clone()
    this.viewer.clock.stopTime = endTime.clone()
    this.viewer.clock.currentTime = startTime.clone()
    this.viewer.clock.shouldAnimate = true
  }

  /**
   * 创建战斗机编队演示
   */
  public createFighterFormation(): void {
    this.demo.createFormationDemo()
  }

  /**
   * 创建单机演示
   */
  public createSingleAircraftDemo(): void {
    this.demo.createDemoAircraft()
  }

  /**
   * 停止所有演示
   */
  public stopAllDemos(): void {
    this.demo.stopDemo()
    this.demo.cleanupFormationDemo()
  }

  /**
   * 更新飘带配置
   */
  public updateRibbonConfig(aircraftId: string, newConfig: {
    color?: Cesium.Color
    width?: number
    maxPoints?: number
  }): void {
    // 先移除现有效果
    this.removeRibbonFromAircraft(aircraftId)
    
    // 重新添加带新配置的效果
    setTimeout(() => {
      this.addRibbonToExistingAircraft(aircraftId, newConfig)
    }, 100)
  }

  /**
   * 生成随机颜色
   */
  private generateRandomColor(): Cesium.Color {
    const colors = [
      Cesium.Color.RED,
      Cesium.Color.BLUE,
      Cesium.Color.GREEN,
      Cesium.Color.YELLOW,
      Cesium.Color.MAGENTA,
      Cesium.Color.CYAN,
      Cesium.Color.ORANGE,
      Cesium.Color.PURPLE
    ]
    
    const randomIndex = Math.floor(Math.random() * colors.length)
    return colors[randomIndex].withAlpha(0.8)
  }

  /**
   * 创建螺旋飞行路径
   */
  public createSpiralFlightPath(center: { lon: number, lat: number, alt: number }, options?: {
    radius?: number
    turns?: number
    duration?: number
    aircraftId?: string
  }): void {
    const radius = options?.radius || 0.01 // 度数
    const turns = options?.turns || 3
    const duration = options?.duration || 60
    const aircraftId = options?.aircraftId || `spiral_aircraft_${Date.now()}`

    const waypoints: Array<{ lon: number, lat: number, alt: number, time: number }> = []
    const steps = 50

    for (let i = 0; i <= steps; i++) {
      const t = i / steps
      const angle = t * turns * 2 * Math.PI
      const currentRadius = radius * (1 - t * 0.5) // 螺旋收缩
      
      const lon = center.lon + currentRadius * Math.cos(angle)
      const lat = center.lat + currentRadius * Math.sin(angle)
      const alt = center.alt + t * 2000 // 逐渐爬升

      waypoints.push({
        lon,
        lat,
        alt,
        time: t * duration
      })
    }

    this.createCustomFlightPath(waypoints, {
      id: aircraftId,
      name: '螺旋飞行',
      color: Cesium.Color.GOLD,
      ribbonColor: Cesium.Color.GOLD.withAlpha(0.7)
    })
  }

  /**
   * 销毁特效管理器
   */
  public destroy(): void {
    this.stopAllDemos()
    this.effectManager.destroy()
  }
}

// 使用示例
export function initializeAircraftRibbonExample(viewer: Cesium.Viewer): AircraftRibbonExample {
  const example = new AircraftRibbonExample(viewer)
  
  // 示例：创建单机演示
  // example.createSingleAircraftDemo()
  
  // 示例：创建编队演示
  // example.createFighterFormation()
  
  // 示例：创建螺旋飞行
  // example.createSpiralFlightPath({ lon: 116.0, lat: 39.0, alt: 5000 })
  
  return example
}
