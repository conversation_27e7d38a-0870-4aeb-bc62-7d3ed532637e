<template>
  <div class="w-full h-24 flex justify-between">
    <left-header-tools class="flex-1"></left-header-tools>
    <MiddleHeader :title="title" class="" />
    <right-header-tools class="flex-1"></right-header-tools>
  </div>
</template>

<script lang="ts" setup>
import MiddleHeader from './middleHeader.vue'
import LeftHeaderTools from './leftHeaderTools.vue'
import RightHeaderTools from './rightHeaderTools.vue'
import { Options, Vue } from 'vue-class-component'
import { defineProps, ref, defineEmits } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from '@/store/index'
const router = useRouter()
const route = useRoute()
const emit = defineEmits(['change', 'delete'])
const props = defineProps({
  msg: String,
})
const title = window.GVJ.URLS.title
;(document as any).title = window.GVJ.URLS.title
</script>

<style scoped lang="sass">
.header
  position: absolute
  top: 0
  width: 100%
  height: 60px
</style>
