<template>
  <div class="box">
    <div class="title">
      <span class="describe">实体列表</span>
      <span class="close" @click.stop="close">关闭</span>
    </div>
    <div class="body">
      <el-tabs
        v-model="activeName"
        type="card"
        class="demo-tabs"
        @tab-click="handleClick"
      >
        <el-tab-pane
          :label="item.title"
          :name="item.name"
          v-for="item in editableTabs"
          :key="item.name"
        >
          <el-tree
            :highlight-current="true"
            id="entity-tree"
            :data="item.content"
            node-key="id"
            default-expand-all
            :render-content="renderContent"
            @node-click="handleNodeClick"
          >
          </el-tree>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div ref="bottom"></div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, reactive, defineEmits } from 'vue'
const emit = defineEmits(['show', 'focusEntity'])
// 列表显示隐藏布尔值
onMounted(() => {
  getList()
})
// 关闭弹层
const close = () => {
  emit('show', 'entityList')
}
// tab值
let activeName = ref('1')
// tab列表
let editableTabs = reactive([
  {
    title: '红方',
    name: '1',
    content: [],
  },
  {
    title: '蓝方',
    name: '2',
    content: [],
  },
])
// 切换tab
const handleClick = (tab: any) => {
  activeName.value = tab.props.name
}
// 列表数据
const getList = () => {
  const ws = new WebSocket(`${(window as any).GVJ.URLS.pluginServer}/getCamp`)
  ws.onopen = () => {}
  ws.onmessage = ev => {
    const data = JSON.parse(ev.data)
    editableTabs[0].content = data[1]
    editableTabs[1].content = data[2]
  }
}
// 树节点点击方法
const handleNodeClick = (e: any) => {
  // 触发父组件事件
  emit('focusEntity', e)
}
// 树形自定义节点内容
const renderContent = (h: any, { node }: { node: { label: string } }) => {
  return h('span', { class: 'custom-tree-node' }, node.label)
}
</script>
<style scoped>
.box {
  width: 11.875vw;
  height: 48.4259vh;
  min-width: 180px;
  min-height: 370px;
  background-color: rgba(16, 37, 63, 0.7);
  /*margin: 20px 6px;*/
  position: absolute;
  right: 20px;
  top: 6.4815vh;
  z-index: 2002;
  border-radius: 5px;
  padding: 0 10px;
  box-sizing: border-box;
  color: #ffffff;
}

.box .title {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  padding: 10px 10px;
  border-bottom: 2px solid #00ffff;
  margin-bottom: 10px;
}

.box .title .close {
  cursor: pointer;
}

.box .title > span:nth-child(1) {
  padding-left: 10px;
  border-left: 4px solid #0ff;
}

.box .body {
  height: 80%;
}

.box .body >>> .el-tabs {
  height: 100%;
}

.box .body >>> .el-tabs .el-tabs__header {
  margin: 0;
  border: none;
  height: unset;
}

.box .body >>> .el-tabs__nav-next,
.box .body >>> .el-tabs__nav-prev {
  line-height: 20px;
  color: #ffffff;
  margin: 3px;
}

.box .body >>> .el-tabs .el-tabs__nav {
  border: none !important;
}

.box .body >>> .el-tabs .el-tabs__item {
  height: 26px;
  line-height: 26px;
  padding: 0 10px !important;
  border: 1px solid aqua !important;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  color: #ffffff !important;
  font-size: 12px;
}

.box .body >>> .el-tabs .el-tabs__item:nth-child(2) {
  border-left: none !important;
}

.box .body >>> .el-tabs .el-tabs__item.is-active {
  color: aqua !important;
  font-size: 16px;
  border-bottom: none !important;
}

.box .body >>> .el-tabs .el-tabs__content {
  border: 1px solid aqua;
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
  border-top-right-radius: 5px;
  border-radius: 0 5px 5px;
  height: 100%;
  overflow-y: auto;
}

.box .body >>> .el-tabs .el-tabs__content::-webkit-scrollbar {
  width: 6px;
  height: 8px;
  position: absolute;
  right: 0;
  bottom: 0;
  border-radius: 5px;
}

.box .body >>> .el-tabs .el-tabs__content::-webkit-scrollbar-thumb {
  background: #00ecff !important;
  border-radius: 0.625rem;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.box .body >>> .el-tree {
  background: none;
  color: #ffffff;
}

.box .body >>> .el-tree-node__content:hover {
  background-color: rgba(16, 37, 63, 0.7);
}

.box .body >>> .el-tree-node:focus > .el-tree-node__content {
  background-color: #00ffff;
}

.box
  .body
  >>> .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  background-color: #00ffff !important;
  color: black;
}
</style>
