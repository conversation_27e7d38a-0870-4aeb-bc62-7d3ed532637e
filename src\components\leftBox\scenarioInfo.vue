<template>
  <div class="box">
    <div class="title">
      <span class="describe">想定信息</span>
      <span class="close" @click.stop="close">关闭</span>
    </div>
    <div class="body">
      <div class="time_box">
        <p class="time">{{ info.simDuration }}</p>
        <p class="text">
          <span style="margin-right: 5px">{{ info.describe }} </span>
          {{ state }}
        </p>
      </div>
      <div class="col">
        <span class="label">运行模式</span>
        <span class="value">{{ info.modelText || '' }}</span>
      </div>
      <div class="col">
        <span class="label">仿真时间</span>
        <span class="value">{{ info.simCurrentTime }}</span>
      </div>
      <div class="col">
        <span class="label">系统时间</span>
        <span class="value">{{ info.nowDate }}</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, defineEmits } from 'vue'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
const emit = defineEmits(['show'])
dayjs.extend(utc)
let info = ref({
  scenarioName: '想定信息',
  model: 2,
  modelText: '未指定',
  describe: '仿真想定描述信息',
  simDuration: dayjs().format('HH:mm:ss'),
  simCurrentTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  nowDate: dayjs().format('YYYY-MM-DD HH:mm:ss'),
})
let state = ref('暂停')
onMounted(() => {
  init()
})

let modelArr = ['数据记录', '数据回放', '未指定']
// 页面初始化
const init = () => {
  getData()
  // 获取态势的实时状态
}
// 获取webSokect数据
const getData = () => {
  const ws = new WebSocket(
    `${(window as any).GVJ.URLS.pluginServer}/getSimulationTime`
  )
  ws.onopen = () => {
    console.log('对海作战链接成功!')
  }
  ws.onmessage = e => {
    const data = JSON.parse(e.data)
    data.simCurrentTime = dayjs(data.simCurrentTime).format(
      'YYYY-MM-DD HH:mm:ss'
    )
    data.simDuration = dayjs(data.simDuration).utc().format('HH:mm:ss')
    data.nowDate = dayjs().format('YYYY-MM-DD HH:mm:ss')
    data.modelText = modelArr[data.model]
    info.value = data
  }
}
// 实时态势状态的毁回调
// 关闭弹层
const close = () => {
  emit('show', 'scenarioInfo')
}
</script>
<style scoped>
.box {
  width: 16.146vw;
  height: 23.0556vh;
  min-width: 310px;
  min-height: 249px;
  background-color: rgba(16, 37, 63, 0.7);
  position: absolute;
  left: 0.525vw;
  top: 6.4815vh;
  z-index: 2002;
  border-radius: 5px;
  padding: 0 10px;
  box-sizing: border-box;
  color: #ffffff;
}

.box .title {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  padding: 10px 10px 15px;
}

.box .title .close {
  cursor: pointer;
}

.box .title > span:nth-child(1) {
  padding-left: 10px;
  border-left: 4px solid #0ff;
}

.box .body {
  padding: 0 10px;
}

.box .body .time_box {
  display: inline-block;
  text-align: center;
  margin-bottom: 40px;
}

.box .body .time_box .time {
  font-size: 22px;
  color: #fa4147;
  font-weight: 600;
  font-family: xiaowei;
}

.box .body .time_box .text {
  font-size: 12px;
  color: aquamarine;
  font-weight: 400;
  font-family: xiaowei;
}

.box .body .col {
  color: #00ffff;
}

.box .body .col:not(:last-child) {
  margin-bottom: 10px;
}

.box .body .col .label {
  border: 1px solid #00ffff;
  border-radius: 5px;
  font-size: 14px;
  margin-right: 10px;
  padding: 0 4px;
}

.box .body .col .value {
  font-size: 14px;
}
</style>
