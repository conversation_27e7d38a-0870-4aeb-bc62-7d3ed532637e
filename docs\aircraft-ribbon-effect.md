# 飞机飘带效果使用指南

## 概述

飞机飘带效果是一个实时更新的视觉特效，能够根据飞机的位置和姿态变化动态生成飘带轨迹。该效果适用于航空仿真、飞行演示和军事可视化等场景。

## 特性

- **实时更新**: 根据飞机位置和姿态实时更新飘带
- **姿态感知**: 基于飞机姿态变化智能添加飘带点
- **性能优化**: 限制飘带点数，自动清理旧数据
- **可定制**: 支持自定义颜色、宽度、长度等参数
- **多机支持**: 可同时为多架飞机添加不同样式的飘带

## 快速开始

### 1. 基本使用

```typescript
import { EffectManager, EffectConfig } from '../utils/effectManager'

// 初始化特效管理器
const effectManager = new EffectManager(viewer, 'ws://your-websocket-url')

// 为现有飞机添加飘带效果
const ribbonConfig: EffectConfig = {
  type: 7, // 飞机飘带效果类型
  modelId: 'your_aircraft_id',
  state: 0, // 创建状态
  color: Cesium.Color.CYAN.withAlpha(0.8),
  customParams: {
    maxPoints: 50,
    width: 8.0,
    minDistance: 10.0
  }
}

effectManager.createEffect(ribbonConfig, 'your_aircraft_id', 7)
```

### 2. 使用示例类

```typescript
import { AircraftRibbonExample } from '../examples/aircraftRibbonExample'

// 初始化示例
const example = new AircraftRibbonExample(viewer)

// 为现有飞机添加飘带
example.addRibbonToExistingAircraft('aircraft_id', {
  color: Cesium.Color.RED.withAlpha(0.8),
  width: 10.0,
  maxPoints: 100
})

// 创建演示
example.createSingleAircraftDemo()
```

## 配置参数

### 飘带配置 (ribbonConfig)

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `maxPoints` | number | 50 | 飘带最大点数 |
| `width` | number | 8.0 | 飘带线宽 |
| `color` | Cesium.Color | CYAN | 飘带颜色 |
| `minDistance` | number | 10.0 | 添加新点的最小距离阈值 |
| `fadeAlpha` | number | 0.1 | 渐变透明度 |

### 效果配置 (EffectConfig)

| 参数 | 类型 | 说明 |
|------|------|------|
| `type` | number | 效果类型，飘带效果为 7 |
| `modelId` | string | 绑定的飞机模型ID |
| `state` | 0\|1 | 0=创建，1=销毁 |
| `color` | Cesium.Color | 飘带颜色 |
| `customParams` | object | 自定义参数 |

## 高级功能

### 1. 编队飞行演示

```typescript
// 创建三机编队演示
example.createFighterFormation()

// 自定义编队配置
const formations = [
  { id: 'leader', color: Cesium.Color.RED },
  { id: 'wingman1', color: Cesium.Color.BLUE },
  { id: 'wingman2', color: Cesium.Color.GREEN }
]
```

### 2. 自定义飞行路径

```typescript
const waypoints = [
  { lon: 116.0, lat: 39.0, alt: 5000, time: 0 },
  { lon: 116.2, lat: 39.1, alt: 6000, time: 20 },
  { lon: 116.5, lat: 39.3, alt: 7000, time: 40 }
]

example.createCustomFlightPath(waypoints, {
  id: 'custom_aircraft',
  name: '自定义飞机',
  color: Cesium.Color.GOLD,
  ribbonColor: Cesium.Color.GOLD.withAlpha(0.7)
})
```

### 3. 螺旋飞行路径

```typescript
example.createSpiralFlightPath(
  { lon: 116.0, lat: 39.0, alt: 5000 },
  {
    radius: 0.01,
    turns: 3,
    duration: 60,
    aircraftId: 'spiral_demo'
  }
)
```

### 4. 批量操作

```typescript
// 为多架飞机添加飘带
const aircraftIds = ['aircraft1', 'aircraft2', 'aircraft3']
const colors = [Cesium.Color.RED, Cesium.Color.BLUE, Cesium.Color.GREEN]

example.addRibbonToMultipleAircraft(aircraftIds, {
  colors: colors,
  width: 12.0,
  maxPoints: 80
})
```

## 性能优化

### 1. 点数限制
- 默认最大50个点，可根据需要调整
- 超出限制时自动移除最旧的点

### 2. 更新策略
- 基于距离阈值（默认10米）
- 基于姿态变化（默认5度）
- 避免频繁更新造成性能问题

### 3. 内存管理
- 自动清理销毁的飘带数据
- 使用对象池减少GC压力

## 故障排除

### 常见问题

1. **飘带不显示**
   - 检查飞机模型ID是否正确
   - 确认飞机实体存在且有position属性
   - 验证特效管理器是否正确初始化

2. **飘带更新不及时**
   - 检查minDistance设置是否过大
   - 确认飞机是否在移动
   - 验证姿态数据是否正确

3. **性能问题**
   - 减少maxPoints数量
   - 增加minDistance阈值
   - 限制同时显示的飘带数量

### 调试方法

```typescript
// 启用调试模式
console.log('飘带数据:', effect.ribbonData)
console.log('飘带点数:', effect.ribbonData?.positions.length)

// 监听更新事件
viewer.scene.preRender.addEventListener(() => {
  // 检查飘带状态
})
```

## API 参考

### EffectManager

#### createEffect(config, name, type)
创建特效

#### destroyEffectByModelId(modelId)
根据模型ID销毁特效

#### updateEffect(id, config)
更新特效配置

### AircraftRibbonExample

#### addRibbonToExistingAircraft(aircraftId, options)
为现有飞机添加飘带

#### removeRibbonFromAircraft(aircraftId)
移除飞机飘带

#### createCustomFlightPath(waypoints, config)
创建自定义飞行路径

#### updateRibbonConfig(aircraftId, newConfig)
更新飘带配置

## 示例项目

完整的示例代码请参考：
- `src/utils/aircraftRibbonDemo.ts` - 演示类
- `src/examples/aircraftRibbonExample.ts` - 使用示例
- `src/utils/effectManager.ts` - 核心实现

## 许可证

本项目遵循 MIT 许可证。
