<!--
 * @Author: 老范
 * @Date: 2025-03-20 17:50:45
 * @LastEditors: 老范
 * @LastEditTime: 2025-05-23 10:56:19
 * @Description: 请填写简介
-->
<template>
  <div id="GEOVISContainer">
    <scenarioInfo></scenarioInfo>
    <logBox></logBox>
    <entityList></entityList>
    <entityInfo></entityInfo>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, ref, defineEmits, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useStore } from 'vuex'
import scenarioInfo from '../components/leftBox/scenarioInfo.vue'
import logBox from '../components/leftBox/logBox.vue'
import entityList from '../components/rightBox/entityList.vue'
import entityInfo from '../components/rightBox/entityInfo.vue'
import { ModelManager } from '../utils/modelControler'
import { EffectManager } from '../utils/effectManager'
type ModelUpdateData = {
  id: string
  lon: number
  lat: number
  height: number
  heading?: number
  type: string
  timestamp: number
}

let GV = window.GV
const store = useStore()

const createView = () => {
  const viewer = new GV.GeoCanvas('GEOVISContainer')
  //  设置天空盒 \Assets\Textures\SkyBox
  const url = `/GV`
  viewer.scene.skyBox = new Cesium.SkyBox({
    sources: {
      positiveX: `${url}/theme1/px.jpg`,
      negativeX: `${url}/theme1/mx.jpg`,
      positiveY: `${url}/theme1/py.jpg`,
      negativeY: `${url}/theme1/my.jpg`,
      positiveZ: `${url}/theme1/pz.jpg`,
      negativeZ: `${url}/theme1/mz.jpg`,
    },
  })
  return viewer
}
function createRadarScan(viewer, options) {
  const {
    center = [115.0, 35.0, 1000.0],
    radius = 5000.0,
    horizontalStartAngle = 0,
    horizontalEndAngle = 90,
    verticalStartAngle = 0,
    verticalEndAngle = 30,
    scanSpeed = 30, // 度/秒
    scanColor = Cesium.Color.CYAN.withAlpha(0.5),
    showScanArea = true,
    active = true,
  } = options

  const toRad = deg => Cesium.Math.toRadians(deg)
  const origin = Cesium.Cartesian3.fromDegrees(...center)

  const radarEntity = viewer.entities.add({
    name: 'Radar Scan Cone',
    position: origin,
    orientation: Cesium.Transforms.headingPitchRollQuaternion(
      origin,
      new Cesium.HeadingPitchRoll(0, 0, 0)
    ),
    ellipsoid: {
      radii: new Cesium.Cartesian3(radius, radius, radius),
      innerRadii: new Cesium.Cartesian3(0.0, 0.0, 0.0),
      minimumClock: toRad(horizontalStartAngle),
      maximumClock: toRad(horizontalEndAngle),
      minimumCone: toRad(verticalStartAngle),
      maximumCone: toRad(verticalEndAngle),
      material: new Cesium.ColorMaterialProperty(scanColor),
      outline: true,
      outlineColor: Cesium.Color.WHITE,
      show: showScanArea,
    },
  })

  // 动态旋转逻辑
  if (active) {
    let angle = 0
    viewer.clock.onTick.addEventListener(clock => {
      angle += scanSpeed * clock._multiplier * clock._tickDeltaSeconds
      angle %= 360
      const heading = Cesium.Math.toRadians(angle)
      radarEntity.orientation = Cesium.Transforms.headingPitchRollQuaternion(
        origin,
        new Cesium.HeadingPitchRoll(heading, 0, 0)
      )
    })
  }

  return radarEntity
}

// 使用示例
const radar = createRadarScan(window.viewer, {
  center: [115, 35, 1000],
  radius: 8000,
  horizontalStartAngle: 0,
  horizontalEndAngle: 90,
  verticalStartAngle: 0,
  verticalEndAngle: 30,
  scanSpeed: 45,
  scanColor: Cesium.Color.LIME.withAlpha(0.4),
  showScanArea: true,
  active: true,
})

window.viewer.camera.flyTo({
  destination: Cesium.Cartesian3.fromDegrees(115, 35, 30000),
  duration: 1.5,
})
const initWebsocket = () => {
  const effectManager = new EffectManager(
    window.viewer,
    `${(window as any).GVJ.URLS.socketServer}api/v1/data-socket/getEvents`
  )
  const modelManager = new ModelManager(window.viewer, effectManager, {
    maxTrailPoints: 200,
    trailColor: Cesium.Color.RED,
  })
  // init模型
  const wsInit = new WebSocket(
    `${
      (window as any).GVJ.URLS.socketServer
    }api/v1/data-socket/getModelData?pushType=init`
  )
  const routeLines: Cesium.Entity[] = []
  wsInit.onmessage = event => {
    const data: any = JSON.parse(event.data)
    console.log('🚀 ~ initWebsocket ~ data:', data)
    console.log(modelManager.effectManager.effects)

    data.init_model.forEach(item => {
      modelManager.createNewModel(item)
      modelManager.trailPrimitive.addTrail(item.na, [
        { lo: item.lo, la: item.la, al: item.al | 0 },
        { lo: item.lo, la: item.la, al: item.al | 0 },
      ])
    })
    data.routes.forEach(route => {
      const routeAry = route.parameters.flat()
      const catrePosition = Cesium.Cartesian3.fromDegreesArrayHeights(routeAry)

      const spline = new Cesium.CatmullRomSpline({
        times: catrePosition.map(
          (_, index) => index / (catrePosition.length - 1)
        ),
        points: catrePosition,
      })
      const smoothPosition: Cesium.Cartesian3[] = []
      const sampleCount = 10
      for (let index = 0; index < sampleCount; index++) {
        const time = index / sampleCount
        smoothPosition.push(spline.evaluate(time))
      }
      const line = window.viewer.entities.add({
        name: '线',
        polyline: {
          width: 1.0,
          positions: catrePosition,
          // material: Cesium.Material.fromType('Color', {
          //   color: Cesium.Color.CORNFLOWERBLUE,
          // }),
          material: Cesium.Color.BLUE.withAlpha(0.2),
          //     material: new Cesium.PolylineGlowMaterialProperty({
          //   glowPower: 0.2,
          //   taperPower: 0.5,
          //   color: Cesium.Color.CORNFLOWERBLUE,
          // }),
        },
      })
      routeLines.push(line)
    })
  }

  // 获取终止状态
  const wsGetState = new WebSocket(
    `${
      (window as any).GVJ.URLS.socketServer
    }api/v1/data-socket/getSysInfo?type=init`
  )
  wsGetState.onmessage = event => {
    const data: any = JSON.parse(event.data)
    if (data.taskStatus === 0) {
      // 清空
      modelManager.reset()
      routeLines.forEach(line => {
        window.viewer.entities.remove(line)
      })
    }
  }
  const wsReal = new WebSocket(
    `${
      (window as any).GVJ.URLS.socketServer
    }api/v1/data-socket/getModelData?pushType=realtime`
  )
  wsReal.onmessage = event => {
    const data: any[] = JSON.parse(event.data)
    modelManager.batchUpdate(data.up, data.ts)
  }
  // 销毁
  const ws1 = new WebSocket(
    `${(window as any).GVJ.URLS.socketServer}api/v1/data-socket/getModelState`
  )
  ws1.onmessage = event => {
    const data: any[] = JSON.parse(event.data)
    data.forEach(item => {
      if (item.param.type == 'disappear') {
        // modelManager.effectManager.renderExplosionEffect(effect)
        setTimeout(() => {
          modelManager.destroyModel(item.name)
        }, 2000)
      } else {
        const effect = {
          config: { modelId: item.name },
        }
        modelManager.effectManager.renderExplosionEffect(effect)
        console.log('🚀 ~ initWebsocket ~ item.name,爆炸:', item.name)
        modelManager.destroyModel(item.name)
      }
    })
  }
}
onMounted(async () => {
  // console.log('getPluginAll',getPluginAll)
  // let list = await getPluginAll()

  // let list = []
  window.viewer = createView()
  window.viewer.scene.debugShowFramesPerSecond = true
  window.GVJ.createdePluginManager(window.viewer, [])
  // 此时添加常用列表
  let arr = window.GVJ.allPlugin.tools.filter(ele => {
    return ele.showMenu
  })
  arr = arr.map(ele => {
    return {
      name: ele.plugName,
    }
  })
  store.commit('SET_TOOLSLIST', arr)
  // 右下角
  arr = window.GVJ.allPlugin.Widgets.filter(ele => {
    return ele.showMenu
  })
  arr = arr.map(ele => {
    return {
      name: ele.plugName,
    }
  })
  store.commit('SET_WIDGETSLIST', arr)
  initWebsocket()
})
</script>

<style scoped>
#GEOVISContainer {
  width: 100%;
  height: 100%;
  position: absolute;
}
</style>
