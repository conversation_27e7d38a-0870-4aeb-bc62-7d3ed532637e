<!--
 * @Author: 老范
 * @Date: 2025-05-12 14:27:16
 * @LastEditors: 老范
 * @LastEditTime: 2025-05-12 15:28:58
 * @Description: 请填写简介
-->
<template>
  <div class="box">
    <div class="title">
      <span class="describe">实体信息</span>
      <span class="close" @click.stop="close">关闭</span>
    </div>
    <div class="body">
      <div class="table-title">
        <div>
          <span>名称</span>
          <span>{{ info.名称 }}</span>
        </div>
        <div>
          <span>ID</span>
          <span>{{ info.ID }}</span>
        </div>
        <div>
          <span>阵营</span>
          <span>{{ info.所属阵营 == 2 ? '红方' : '蓝方' }}</span>
        </div>
        <div>
          <span>俯仰</span>
          <span>{{ info.俯仰角 }}</span>
        </div>
        <div>
          <span>偏航</span>
          <span>{{ info.偏航角 }}</span>
        </div>
        <div>
          <span>翻滚</span>
          <span>{{ info.翻滚度 }}</span>
        </div>
        <div>
          <span>经度</span>
          <span>{{ info.经度 }}</span>
        </div>
        <div>
          <span>纬度</span>
          <span>{{ info.纬度 }}</span>
        </div>
        <div>
          <span>高度</span>
          <span>{{ info.高度 }}</span>
        </div>
        <div>
          <span>速度</span>
          <span>{{ info.速度 }}</span>
        </div>
      </div>
      <div class="table-body"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted, defineProps, watch, defineEmits } from 'vue'
interface InfoType {
  纬度: number
  经度: number
  高度: number
  名称: string
  模型状态: string
  所属阵营: number
  ID: number
  俯仰角: number
  偏航角: number
  翻滚度: number
}
const emit = defineEmits(['show'])
let props = defineProps<{
  id: number
}>()
let ws = ref<any>({})
let info = ref<InfoType | {}>({})
watch(
  () => props.id,
  newVal => {
    props.id = newVal
    ws.send(JSON.stringify({ modelId: props.id.toString() }))
  }
)
onMounted(() => {
  init()
})
const init = () => {
  ws = new WebSocket(
    `${(window as any).GVJ.URLS.pluginServer}/getModelInstance`
  )
  ws.onopen = () => {
    console.log('链接成功')
    ws.send(JSON.stringify({ modelId: props.id.toString() }))
  }
  ws.onmessage = (e: any) => {
    info.value = JSON.parse(e.data)
  }
}
// 关闭弹层
const close = () => {
  ws.close()
  emit('show', 'entityRealTime')
}
</script>
<style scoped>
.box {
  width: 11.875vw;
  height: 18.4259vh;
  min-width: 180px;
  min-height: 270px;
  background-color: rgba(16, 37, 63, 0.7);
  /*margin: 20px 6px;*/
  position: absolute;
  right: 20px;
  top: 55.4815vh;
  z-index: 2002;
  box-sizing: border-box;
  border-radius: 5px 5px 0 0;
  color: #fff;
  padding-bottom: 20px;
}
.box .title {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  padding: 10px 10px;
  border-bottom: 2px solid #00ffff;
  margin-bottom: 10px;
}

.box .title > span:nth-child(1) {
  padding-left: 10px;
  border-left: 4px solid #0ff;
}
.box .title .close {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
}

.box .title > p {
  text-align: center;
}
/* 
.box .body {
  padding: 0 15px;
} */

.box .body .table-title {
  display: flex;
  justify-content: space-between;
  color: aqua;
  background: #0c2e4c;
  padding: 15px;
  flex-direction: column;
  font-size: 12px;
  margin-bottom: 10px;
}

.box .body .table-title > div > span:nth-child(1) {
  border: 1px solid #00e0ff;
  border-radius: 3px;
  margin-right: 10px;
  padding: 0 4px;
}
.box .body .t-l {
  text-align: left !important;
  padding-left: 60px !important;
}
</style>
