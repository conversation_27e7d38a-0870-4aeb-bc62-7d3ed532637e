/*
 * @Author: 老范
 * @Date: 2025-04-01 15:25:08
 * @LastEditors: 老范
 * @LastEditTime: 2025-04-28 19:12:02
 * @Description: 请填写简介
 */
// import * as Cesium from 'cesium'
import { getModels } from '../api/cjgl/situation'
import { TrailManager } from './primitive'
// 类型定义

type ModelUpdateData = {
  na: string
  ic: string
  lo: number
  la: number
  al: number
  si: string
  ya?: number // yaw
  pi?: number //pitch
  ro?: number //roll
  type: string
}

type ModelConfig = {
  maxTrailPoints: number // 尾迹最大点数
  trailWidth: number // 尾迹线宽
  trailColor: Cesium.Color
  modelCacheSize: number // 模型池缓存数量
  lodTransitionMargin: number // 防止LOD频繁切换的缓冲距离
  maxTrailPointPoolSize: number // 对象池最大容量
  trailPointPoolInitialSize: number // 初始池大小
  lodDistanceThresholds: {
    // LOD距离阈值
    high: number
    medium: number
    low: number
  }
}

export class ModelManager {
  private viewer: Cesium.Viewer
  private config: ModelConfig
  // 模型池

  private modelPool = new Map<string, Cesium.Model>()

  private positionPool = new Map<string, Cesium.Cartesian3>()

  // 待回收模型队列

  private recycleQueue: string[] = []
  // trailSystem: TrailSystem;
  trailPrimitive: any
  effectManager: any

  constructor(
    viewer: Cesium.Viewer,
    effectManager: any,
    config?: Partial<ModelConfig>
  ) {
    this.viewer = viewer
    this.effectManager = effectManager

    this.config = {
      maxTrailPoints: 10,
      trailWidth: 3,
      trailColor: Cesium.Color.CYAN.withAlpha(0.7),
      modelCacheSize: 2000,
      lodTransitionMargin: 500, // 单位：米
      maxTrailPointPoolSize: 5000, // 最多缓存5000个点对象
      trailPointPoolInitialSize: 10, // 初始化时预创建1000个
      lodDistanceThresholds: {
        high: 5000, // 5km内高精度模型
        medium: 10000, // 10km中精度
        low: 20000, // 20km以上低精度
      },
      ...config,
    }
    this.trailPrimitive = new TrailManager(viewer)
  }

  // 批量更新模型 (主入口)

  public async batchUpdate(data: ModelUpdateData[]) {
    const cameraPosition = this.viewer.camera.positionWC
    // let atime = Date.now();

    data.forEach(item => {
      // 1. 位置转换
      const position = Cesium.Cartesian3.fromDegrees(
        item.lo,
        item.la,
        item.al | 0
      )
      // 2. 模型存在性检查
      if (this.modelPool.has(item.na)) {
        this.updateExistingModel(item.na, position, item)
        this.trailPrimitive.updateTrail(item.na, [
          { lo: item.lo, la: item.la, al: item.al | 0 },
        ])
      } else {
        // this.createNewModel(item, position)
        // this.trailPrimitive.addTrail(item.na, [
        //   { lo: item.lo, la: item.la, al: item.al | 0 },
        //   { lo: item.lo, la: item.la, al: item.al | 0 },
        // ])
      }
      // 3. 更新尾迹
      // 4. 更新位置池
      // this.positionPool.set(item.na, position);
      // 5. LOD控制
      // this.applyLod(item.na, position, cameraPosition);
    })
    // this.trailPrimitive.batchUpdate(data);
    // 6. 清理不可见模型
    // this.cleanupInvisibleModels(cameraPosition);
  }

  // 创建新模型

  public async createNewModel(item: ModelUpdateData) {
    const modelColor =
      item.si === 'red'
        ? Cesium.Color.RED.withAlpha(1.0)
        : Cesium.Color.BLUE.withAlpha(1.0)
    try {
      const position = Cesium.Cartesian3.fromDegrees(
        item.lo,
        item.la,
        item.al | 0
      )
      // const model = getModels({ name: item.ic })
      const model = await this.loadModelWithLod(item.ic, position)
      console.log('创建')

      // 预加载所有LOD级别
      //   this.preloadLodModels(item.type);
      const entity = this.viewer.entities.add({
        id: item.na,
        position: new Cesium.ConstantPositionProperty(position),
        // model: {
        //   uri: '/assets/models/f18.glb',
        //   minimumPixelSize: 128,
        //   maximumScale: 512,
        // },
        model: {
          uri: model.uri,
          minimumPixelSize: 128,
          maximumScale: 512,
          color: modelColor,
        },

        orientation: this.calculateOrientation(
          position,
          item.ya,
          item.pi,
          item.ro
        ),
        label: {
          text: item.na,
          show: true,
          font: '16px sans-serif',
          showBackground: false,
          fillColor: modelColor,
          pixelOffset: new Cesium.Cartesian3(0, -30, 0),
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 1.5,
        },
      })
      // 加入对象池
      this.modelPool.set(item.na, entity.model)

      // 为飞机类型的模型添加飘带效果
      if (
        item.type === 'aircraft' ||
        item.ic.toLowerCase().includes('aircraft') ||
        item.ic.toLowerCase().includes('plane')
      ) {
        this.addRibbonEffect(item.na, item.si)
      }

      // 管理回收队列
      if (this.recycleQueue.length >= this.config.modelCacheSize) {
        const oldId = this.recycleQueue.shift()!

        this.destroyModel(oldId)
      }

      this.recycleQueue.push(item.na)
    } catch (error) {
      console.error(`Failed to load model ${item.type}:`, error)
    }
  }
  private preloadLodModels(type: string) {
    ;['high', 'medium', 'low'].forEach(lod => {
      const uri = `models/${type}_${lod}.glb`
      if (!this.modelCache.has(uri)) {
        this.loadModelWithCache(uri)
      }
    })
  }
  // 在 ModelManager 类中添加以下方法
  private applyLod(
    id: string,
    modelPosition: Cesium.Cartesian3,
    cameraPosition: Cesium.Cartesian3
  ) {
    const modelEntity = this.viewer.entities.getById(id)
    if (!modelEntity || !modelEntity.model) return

    // 计算与相机的距离
    const distance = Cesium.Cartesian3.distance(modelPosition, cameraPosition)

    // 获取当前模型配置
    const currentUri = modelEntity.model.uri?.getValue(Cesium.JulianDate.now())
    const modelType = this.getModelTypeFromUri(currentUri) // 从URI解析类型
    const currentLod = this.getCurrentLodLevel(currentUri)
    const distanceToHigh = this.config.lodDistanceThresholds.high
    const distanceToMedium = this.config.lodDistanceThresholds.medium
    const margin = this.config.lodTransitionMargin

    let targetLod = 'medium'
    switch (currentLod) {
      case 'high':
        targetLod = distance > distanceToHigh + margin ? 'medium' : 'high'
        break

      case 'medium':
        if (distance < distanceToHigh - margin) {
          targetLod = 'high'
        } else if (distance > distanceToMedium + margin) {
          targetLod = 'low'
        } else {
          targetLod = 'medium'
        }
        break

      case 'low':
        targetLod = distance < distanceToMedium - margin ? 'medium' : 'low'
        break
    }

    // 如果LOD需要更新
    // if (!currentUri?.includes(targetLod)) {
    //   const newUri = `models/${modelType}_${targetLod}.glb`;

    //   // 异步加载新模型（带缓存检查）
    //   this.loadModelWithCache(newUri)
    //     .then(() => {
    //       modelEntity.model!.uri = newUri;

    //       // 保持模型方向一致性
    //       if (modelEntity.orientation) {
    //         modelEntity.orientation = modelEntity.orientation.clone();
    //       }
    //     })
    //     .catch(err => {
    //       console.warn(`LOD update failed for ${id}:`, err);
    //     });
    // }
  }
  private getCurrentLodLevel(uri?: string): 'high' | 'medium' | 'low' {
    if (!uri) return 'medium' // 默认值

    // 匹配URI中的LOD层级（假设URI格式：models/[type]_[lod].glb）
    const lodMatch = uri.match(/_([a-z]+)\.glb$/i)

    if (lodMatch && lodMatch[1]) {
      const lod = lodMatch[1].toLowerCase() as any
      return ['high', 'medium', 'low'].includes(lod) ? lod : 'medium' // 无效值回退
    }

    return 'medium' // 默认值
  }
  // 添加辅助方法
  private modelCache = new Map<string, Promise<void>>()

  private async loadModelWithCache(uri: string): Promise<void> {
    // 已加载的模型直接返回
    if (this.modelCache.has(uri)) {
      return this.modelCache.get(uri)!
    }

    const promise = new Promise<void>((resolve, reject) => {
      // 使用Cesium内置资源加载器
      Cesium.Resource.fetchArrayBuffer(uri)
        .then(() => resolve())
        .catch(reject)
    })

    this.modelCache.set(uri, promise)
    return promise
  }

  private getModelTypeFromUri(uri?: string): string {
    if (!uri) return 'default'
    const matches = uri.match(/models\/(.*?)_/)
    return matches ? matches[1] : 'default'
  }
  // 加载带LOD的模型

  private async loadModelWithLod(type: string, position: Cesium.Cartesian3) {
    const distance = this.calculateDistanceToCamera(position)

    let uri: string

    // if (distance < this.config.lodDistanceThresholds.high) {
    //   uri = `models/${type}_high.glb`;
    // } else if (distance < this.config.lodDistanceThresholds.medium) {
    //   uri = `models/${type}_medium.glb`;
    // } else {
    //   uri = `models/${type}_low.glb`;
    // }

    // 这里可以添加模型预加载逻辑
    return {
      uri: `${
        (window as any).GVJ.URLS.pluginServer
      }api/v1/model3d/glb/view?name=${type}`,
    }

    // return { uri: `/assets/models/${type}.glb` }
  }

  // 更新现有模型

  private updateExistingModel(
    id: string,
    newPosition: Cesium.Cartesian3,
    item: {
      na?: string
      lo?: number
      la?: number
      al?: number
      ya: any
      pi: any
      ro: any
      type?: string
    }
  ) {
    const entity = this.viewer.entities.getById(id)
    if (entity) {
      entity.position = newPosition // 让Cesium内部处理矩阵计算
      this.positionPool.set(id, newPosition.clone())
      if (item.ya) {
        const orientation = this.calculateOrientation(
          newPosition,
          item.ya ?? 0,
          item.pi ?? 0,
          item.ro ?? 0
        )
        entity.orientation = new Cesium.ConstantProperty(orientation)
      }
    }
  }

  // 计算模型方向
  private calculateOrientation(
    position: Cesium.Cartesian3,
    yaw: number | undefined,
    pitch: number | undefined,
    roll: number | undefined
  ) {
    return Cesium.Transforms.headingPitchRollQuaternion(
      position,
      Cesium.HeadingPitchRoll.fromDegrees(yaw, pitch, roll),
      Cesium.Ellipsoid.WGS84,
      Cesium.Transforms.northWestUpToFixedFrame
    )
  }
  // 清理不可见模型

  private cleanupInvisibleModels(cameraPosition: Cesium.Cartesian3) {
    this.positionPool.forEach((position, id) => {
      if (!this.isPositionVisible(position, cameraPosition)) {
        this.destroyModel(id)
      }
    })
  }
  // 判断位置是否在视锥内
  private isPositionVisible(
    position: Cesium.Cartesian3,
    cameraPosition: Cesium.Cartesian3
  ): boolean {
    const distance = Cesium.Cartesian3.distance(position, cameraPosition)
    return distance < this.config.lodDistanceThresholds.low * 2
  }
  // 销毁模型

  public destroyModel(id: string) {
    const model = this.modelPool.get(id)
    const trail = this.trailPrimitive.trails.get(id)
    if (model) {
      this.viewer.entities.removeById(id)
      this.modelPool.delete(id)
      this.positionPool.delete(id)
    }
    if (trail) {
      this.trailPrimitive.removeTrail(id)
    }
    this.effectManager.destroyEffectByModelId(id)
    // this.effectManager.destroy()
  }

  // 计算到相机的距离

  private calculateDistanceToCamera(position: Cesium.Cartesian3): number {
    return Cesium.Cartesian3.distance(position, this.viewer.camera.positionWC)
  }

  // 重置所有模型
  public reset() {
    this.modelPool.forEach((_, id) => {
      this.destroyModel(id)
      this.trailPrimitive.removeTrail(id)
    })
    this.effectManager.destroy()
  }

  /**
   * 为飞机添加飘带效果
   */
  private addRibbonEffect(modelId: string, side: string): void {
    try {
      const ribbonColor =
        side === 'red'
          ? Cesium.Color.RED.withAlpha(0.8)
          : Cesium.Color.BLUE.withAlpha(0.8)

      const ribbonConfig = {
        type: 7 as const, // 飞机飘带效果类型
        modelId: modelId,
        state: 0 as const, // 创建状态
        color: ribbonColor,
        customParams: {
          maxPoints: this.config.maxTrailPoints,
          width: this.config.trailWidth,
          minDistance: 15.0,
          fadeAlpha: 0.1,
        },
      }

      this.effectManager.createEffect(ribbonConfig, modelId, 7)
      console.log(`Added ribbon effect for aircraft: ${modelId}`)
    } catch (error) {
      console.error(`Failed to add ribbon effect for ${modelId}:`, error)
    }
  }

  /**
   * 移除飞机飘带效果
   */
  public removeRibbonEffect(modelId: string): void {
    this.effectManager.destroyEffectByModelId(modelId)
  }

  /**
   * 更新飞机飘带配置
   */
  public updateRibbonConfig(
    modelId: string,
    config: {
      color?: Cesium.Color
      width?: number
      maxPoints?: number
    }
  ): void {
    // 先移除现有效果
    this.removeRibbonEffect(modelId)

    // 延迟重新添加，确保清理完成
    setTimeout(() => {
      const entity = this.viewer.entities.getById(modelId)
      if (entity) {
        const ribbonConfig = {
          type: 7 as const,
          modelId: modelId,
          state: 0 as const,
          color: config.color || Cesium.Color.CYAN.withAlpha(0.8),
          customParams: {
            maxPoints: config.maxPoints || this.config.maxTrailPoints,
            width: config.width || this.config.trailWidth,
            minDistance: 15.0,
            fadeAlpha: 0.1,
          },
        }

        this.effectManager.createEffect(ribbonConfig, modelId, 7)
      }
    }, 100)
  }
}
