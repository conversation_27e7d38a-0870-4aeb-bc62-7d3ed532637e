/**
 * 飞机飘带效果演示
 * 展示如何为飞机模型添加实时更新的飘带效果
 */

import { EffectManager, EffectConfig } from './effectManager'

export class AircraftRibbonDemo {
  private viewer: Cesium.Viewer
  private effectManager: EffectManager
  private aircraftEntity: Cesium.Entity | null = null
  private animationId: number | null = null

  constructor(viewer: Cesium.Viewer, effectManager: EffectManager) {
    this.viewer = viewer
    this.effectManager = effectManager
  }

  /**
   * 创建演示飞机并添加飘带效果
   */
  public async createDemoAircraft(): Promise<void> {
    // 创建飞行路径
    const flightPath = this.generateFlightPath()
    
    // 创建飞机实体
    this.aircraftEntity = this.viewer.entities.add({
      id: 'demo_aircraft',
      name: '演示飞机',
      position: flightPath.property,
      orientation: new Cesium.VelocityOrientationProperty(flightPath.property),
      model: {
        uri: '/assets/models/f18.glb', // 替换为实际的飞机模型路径
        minimumPixelSize: 64,
        maximumScale: 200,
        scale: 1.0,
        color: Cesium.Color.CYAN
      },
      label: {
        text: '演示飞机',
        font: '14pt sans-serif',
        pixelOffset: new Cesium.Cartesian2(0, -40),
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE
      }
    })

    // 添加飞机飘带效果
    this.addRibbonEffect()

    // 开始动画
    this.startAnimation(flightPath.duration)
  }

  /**
   * 添加飞机飘带效果
   */
  private addRibbonEffect(): void {
    if (!this.aircraftEntity) return

    const ribbonConfig: EffectConfig = {
      type: 7, // 飞机飘带效果类型
      modelId: this.aircraftEntity.id,
      state: 0, // 创建状态
      color: Cesium.Color.CYAN.withAlpha(0.8),
      customParams: {
        maxPoints: 100,
        width: 12.0,
        fadeAlpha: 0.1
      }
    }

    this.effectManager.createEffect(ribbonConfig, this.aircraftEntity.id, 7)
  }

  /**
   * 生成飞行路径
   */
  private generateFlightPath(): { property: Cesium.SampledPositionProperty, duration: number } {
    const startTime = Cesium.JulianDate.now()
    const duration = 120 // 2分钟飞行时间

    const property = new Cesium.SampledPositionProperty()

    // 定义关键飞行点
    const waypoints = [
      { lon: 116.0, lat: 39.0, alt: 5000, time: 0 },
      { lon: 116.2, lat: 39.1, alt: 6000, time: 20 },
      { lon: 116.5, lat: 39.3, alt: 7000, time: 40 },
      { lon: 116.8, lat: 39.2, alt: 6500, time: 60 },
      { lon: 117.0, lat: 39.0, alt: 5500, time: 80 },
      { lon: 117.2, lat: 38.8, alt: 5000, time: 100 },
      { lon: 117.5, lat: 38.6, alt: 4500, time: 120 }
    ]

    // 添加样本点
    waypoints.forEach(point => {
      const time = Cesium.JulianDate.addSeconds(startTime, point.time, new Cesium.JulianDate())
      const position = Cesium.Cartesian3.fromDegrees(point.lon, point.lat, point.alt)
      property.addSample(time, position)
    })

    // 设置插值算法
    property.setInterpolationOptions({
      interpolationDegree: 2,
      interpolationAlgorithm: Cesium.HermitePolynomialApproximation
    })

    return { property, duration }
  }

  /**
   * 开始动画
   */
  private startAnimation(duration: number): void {
    const startTime = Cesium.JulianDate.now()
    const endTime = Cesium.JulianDate.addSeconds(startTime, duration, new Cesium.JulianDate())

    // 设置时钟
    this.viewer.clock.startTime = startTime.clone()
    this.viewer.clock.stopTime = endTime.clone()
    this.viewer.clock.currentTime = startTime.clone()
    this.viewer.clock.clockRange = Cesium.ClockRange.LOOP_STOP
    this.viewer.clock.multiplier = 1

    // 跟踪飞机
    if (this.aircraftEntity) {
      this.viewer.trackedEntity = this.aircraftEntity
    }

    // 开始播放
    this.viewer.clock.shouldAnimate = true
  }

  /**
   * 停止演示
   */
  public stopDemo(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }

    if (this.aircraftEntity) {
      this.viewer.entities.remove(this.aircraftEntity)
      this.effectManager.destroyEffectByModelId(this.aircraftEntity.id)
      this.aircraftEntity = null
    }

    this.viewer.trackedEntity = undefined
    this.viewer.clock.shouldAnimate = false
  }

  /**
   * 创建多架飞机编队演示
   */
  public async createFormationDemo(): Promise<void> {
    const formations = [
      { id: 'leader', offset: { x: 0, y: 0, z: 0 }, color: Cesium.Color.RED },
      { id: 'wingman1', offset: { x: -100, y: -50, z: -20 }, color: Cesium.Color.BLUE },
      { id: 'wingman2', offset: { x: 100, y: -50, z: -20 }, color: Cesium.Color.GREEN }
    ]

    const baseFlightPath = this.generateFlightPath()

    formations.forEach((formation, index) => {
      setTimeout(() => {
        this.createFormationAircraft(formation, baseFlightPath.property)
      }, index * 1000) // 延迟创建，形成编队效果
    })
  }

  /**
   * 创建编队飞机
   */
  private createFormationAircraft(
    formation: { id: string, offset: { x: number, y: number, z: number }, color: Cesium.Color },
    basePath: Cesium.SampledPositionProperty
  ): void {
    // 创建偏移路径
    const offsetPath = new Cesium.SampledPositionProperty()
    
    // 复制基础路径并添加偏移
    const times = basePath.times
    times.forEach(time => {
      const basePosition = basePath.getValue(time)
      if (basePosition) {
        const offsetPosition = new Cesium.Cartesian3(
          basePosition.x + formation.offset.x,
          basePosition.y + formation.offset.y,
          basePosition.z + formation.offset.z
        )
        offsetPath.addSample(time, offsetPosition)
      }
    })

    // 创建飞机实体
    const aircraft = this.viewer.entities.add({
      id: `formation_${formation.id}`,
      name: `编队飞机 ${formation.id}`,
      position: offsetPath,
      orientation: new Cesium.VelocityOrientationProperty(offsetPath),
      model: {
        uri: '/assets/models/f18.glb',
        minimumPixelSize: 48,
        maximumScale: 150,
        scale: 0.8,
        color: formation.color
      }
    })

    // 添加飘带效果
    const ribbonConfig: EffectConfig = {
      type: 7,
      modelId: aircraft.id,
      state: 0,
      color: formation.color.withAlpha(0.6),
      customParams: {
        maxPoints: 80,
        width: 8.0
      }
    }

    this.effectManager.createEffect(ribbonConfig, aircraft.id, 7)
  }

  /**
   * 清理编队演示
   */
  public cleanupFormationDemo(): void {
    const formations = ['leader', 'wingman1', 'wingman2']
    formations.forEach(id => {
      const entityId = `formation_${id}`
      const entity = this.viewer.entities.getById(entityId)
      if (entity) {
        this.viewer.entities.remove(entity)
        this.effectManager.destroyEffectByModelId(entityId)
      }
    })
  }
}
