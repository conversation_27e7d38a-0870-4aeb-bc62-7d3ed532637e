/*
 * @Author: 老范
 * @Date: 2025-04-09 09:25:21
 * @LastEditors: 老范
 * @LastEditTime: 2025-04-21 09:56:23
 * @Description: 请填写简介
 */
// 配置参数
const MAX_POINTS = 500
const WGS84_RADIUS = 6378137.0
const WGS84_FLATTENING = 1 / 298.257223563

// 预计算参数
const eSquared = 2 * WGS84_FLATTENING - WGS84_FLATTENING * WGS84_FLATTENING

// trailDataProcessor.js
// 坐标转换缓存
const coordCache = new Float64Array(3)
const sinCache = new Float64Array(2)
const cosCache = new Float64Array(2)
function clamp(value, min, max) {
  return Math.min(Math.max(value, min), max)
}
function toRadians(degrees) {
  return (degrees * Math.PI) / 180.0
}

function wgs84ToEcef(lon, lat, alt) {
  // 输入校验
  lon = typeof lon === 'number' ? clamp(lon, -180, 180) : 0
  lat = typeof lat === 'number' ? clamp(lat, -90, 90) : 0
  alt = typeof alt === 'number' ? alt : 0

  // 转换计算
  const λ = toRadians(lon)
  const φ = toRadians(lat)
  // ...（其余计算逻辑保持不变）
  // 缓存三角函数值
  sinCache[0] = Math.sin(λ)
  cosCache[0] = Math.cos(λ)
  sinCache[1] = Math.sin(φ)
  cosCache[1] = Math.cos(φ)

  const N = WGS84_RADIUS / Math.sqrt(1 - eSquared * sinCache[1] * sinCache[1])

  coordCache[0] = (N + alt) * cosCache[1] * cosCache[0]
  coordCache[1] = (N + alt) * cosCache[1] * sinCache[0]
  coordCache[2] = (N * (1 - eSquared) + alt) * sinCache[1]

  // 输出校验
  if (isNaN(coordCache[0])) coordCache[0] = 0
  if (isNaN(coordCache[1])) coordCache[1] = 0
  if (isNaN(coordCache[2])) coordCache[2] = 0

  return coordCache
}

class TrailBuffer {
  constructor() {
    this.positions = new Float64Array(MAX_POINTS * 3)
    this.count = 0
  }

  addPoint(x, y, z) {
    if (this.count >= MAX_POINTS) {
      this.positions.copyWithin(0, 3)
      this.count--
    }
    const idx = this.count * 3
    this.positions[idx] = x
    this.positions[idx + 1] = y
    this.positions[idx + 2] = z
    this.count++
  }

  getPositions() {
    return this.positions.slice(0, this.count * 3)
  }
}

const trails = new Map()
self.onmessage = function (e) {
  const { type, trailId, rawData, useTransferableBuffer, settings } = e.data
  try {
    if (type === 'REMOVE_TRAIL') {
      trails.delete(trailId)
      return
    }
    if (!trails.has(trailId)) {
      trails.set(trailId, {
        buffer: new TrailBuffer(),
        settings: settings,
      })
    }
    const trail = trails.get(trailId)
    const cleanData = rawData.map(p => ({
      la: Number(p.la) || 0,
      lo: Number(p.lo) || 0,
      al: Number(p.al) || 0,
    }))
    cleanData.forEach(point => {
      const ecef = wgs84ToEcef(point.lo, point.la, point.al)
      trail.buffer.addPoint(ecef[0], ecef[1], ecef[2])
    })
    const finalData = trail.buffer.getPositions() // Float64Array
    if (useTransferableBuffer) {
      self.postMessage(
        {
          type: type === 'CREATE_TRAIL' ? 'TRAIL_CREATED' : 'TRAIL_UPDATE',
          trailId,
          buffer: finalData.buffer,
          count: finalData.length / 3,
        },
        [finalData.buffer]
      )
    } else {
      self.postMessage({
        type: type === 'CREATE_TRAIL' ? 'TRAIL_CREATED' : 'TRAIL_UPDATE',
        trailId,
        positions: finalData,
      })
    }
  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      trailId,
      error: error.message,
    })
  }
}
