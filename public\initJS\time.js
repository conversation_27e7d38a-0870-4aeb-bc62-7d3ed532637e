/*
 * @Author: 老范
 * @Date: 2025-05-09 10:08:53
 * @LastEditors: 老范
 * @LastEditTime: 2025-05-12 14:19:34
 * @Description: 请填写简介
 */

window.GVJ.Times = [
  {
    name:'军用标准时间',
    type:'local',
    date:new Date()
  },
  {
    name:'作战时间',
    type:'update',
    callback:onUpdateTime2
  },
  // {
  //   name:'作战时间',
  //   type:'cesium'
  // }
]
function onUpdateTime2(callback){
  setInterval(() => {
    callback(new Date(new Date().getTime()-1000*60*60))
  }, 1000);
}

